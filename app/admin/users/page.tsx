"use client"

import { useEffect, useState, useCallback } from "react"
import { useRouter } from "next/navigation"
import { useAdminAuth } from "@/hooks/useAdminAuth"
import { useDataStore } from "@/hooks/useDataStore"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { formatDateTime } from "@/lib/utils"
import { Users, Search, Filter, UserPlus, Edit, Trash2 } from "lucide-react"

// Mock user data for demonstration
const mockUsers = [
  {
    id: "user_1",
    name: "<PERSON>",
    email: "<EMAIL>",
    type: "buyer",
    company: "Tech Corp",
    location: "Bangalore",
    isActive: true,
    createdAt: new Date("2024-01-15"),
  },
  {
    id: "user_2", 
    name: "<PERSON>",
    email: "<EMAIL>",
    type: "supplier",
    company: "Fleet Services Ltd",
    location: "Mumbai",
    isActive: true,
    createdAt: new Date("2024-01-20"),
  },
  {
    id: "user_3",
    name: "<PERSON>",
    email: "<EMAIL>", 
    type: "rider",
    company: "Independent",
    location: "Delhi",
    isActive: false,
    createdAt: new Date("2024-02-01"),
  },
]

export default function AdminUsers() {
  const router = useRouter()
  const { user: currentUser } = useAdminAuth()
  const { dataStore, isInitialized } = useDataStore()
  const [loading, setLoading] = useState(true)
  const [users, setUsers] = useState(mockUsers)
  const [searchTerm, setSearchTerm] = useState("")
  const [filterType, setFilterType] = useState("")
  const [filteredUsers, setFilteredUsers] = useState(mockUsers)

  const loadData = useCallback(async () => {
    if (!isInitialized || !currentUser) return

    try {
      setLoading(true)
      // In a real app, you'd fetch users from an API
      // For now, we'll use mock data
      setUsers(mockUsers)
    } catch (err) {
      console.error("Failed to load users:", err)
    } finally {
      setLoading(false)
    }
  }, [currentUser, isInitialized])

  useEffect(() => {
    if (!currentUser || currentUser.type !== "admin") {
      router.push("/admin/login")
      return
    }
    loadData()
  }, [currentUser, router, loadData])

  useEffect(() => {
    let filtered = users.filter(user => 
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.company.toLowerCase().includes(searchTerm.toLowerCase())
    )

    if (filterType) {
      filtered = filtered.filter(user => user.type === filterType)
    }

    setFilteredUsers(filtered)
  }, [users, searchTerm, filterType])

  const getUserTypeColor = (type: string) => {
    switch (type) {
      case 'buyer': return 'bg-status-pending text-status-pending-foreground'
      case 'supplier': return 'bg-status-bidding text-status-bidding-foreground'
      case 'rider': return 'bg-status-matched text-status-matched-foreground'
      case 'admin': return 'bg-primary text-primary-foreground'
      default: return 'bg-muted text-muted-foreground'
    }
  }

  const getStatusColor = (isActive: boolean) => {
    return isActive 
      ? 'bg-status-completed text-status-completed-foreground'
      : 'bg-status-cancelled text-status-cancelled-foreground'
  }

  if (!currentUser || currentUser.type !== "admin") {
    return null
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-foreground">User Management</h1>
          <p className="text-muted-foreground">Manage all platform users and their permissions</p>
        </div>
        <Button className="flex items-center gap-2">
          <UserPlus className="h-4 w-4" />
          Add User
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search users..."
                className="pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <select
              className="w-full h-9 px-3 py-1 border border-input rounded-md text-sm bg-background text-foreground"
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
            >
              <option value="">All User Types</option>
              <option value="buyer">Buyers</option>
              <option value="supplier">Suppliers</option>
              <option value="rider">Riders</option>
              <option value="admin">Admins</option>
            </select>
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">
                {filteredUsers.length} of {users.length} users
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Users List */}
      <Card>
        <CardHeader>
          <CardTitle>Platform Users</CardTitle>
          <CardDescription>All registered users across the platform</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="text-muted-foreground mt-2">Loading users...</p>
            </div>
          ) : filteredUsers.length === 0 ? (
            <div className="text-center py-8">
              <Users className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-2 text-sm font-medium text-foreground">No users found</h3>
              <p className="mt-1 text-sm text-muted-foreground">
                Try adjusting your search or filters.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredUsers.map((user) => (
                <div
                  key={user.id}
                  className="flex items-center justify-between p-4 border border-border rounded-lg hover:bg-muted/50 transition-colors"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h4 className="font-medium text-foreground">{user.name}</h4>
                      <Badge className={getUserTypeColor(user.type)}>
                        {user.type}
                      </Badge>
                      <Badge className={getStatusColor(user.isActive)}>
                        {user.isActive ? 'Active' : 'Inactive'}
                      </Badge>
                    </div>
                    <div className="flex items-center text-sm text-muted-foreground space-x-4">
                      <span>{user.email}</span>
                      <span>•</span>
                      <span>{user.company}</span>
                      <span>•</span>
                      <span>{user.location}</span>
                      <span>•</span>
                      <span>Joined {formatDateTime(user.createdAt)}</span>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm">
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="sm" className="text-destructive hover:text-destructive">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
